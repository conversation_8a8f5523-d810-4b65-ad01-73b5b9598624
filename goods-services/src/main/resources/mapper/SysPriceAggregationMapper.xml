<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.goods.mapper.SysPriceAggregationMapper">

    <!-- 查询价格聚合数据 -->
    <select id="selectPriceAggregation" resultType="com.knet.goods.model.dto.resp.PriceAggregationResp">
        SELECT sku, spec, min_price as minPrice, max_price as maxPrice, version, create_time as createTime
        FROM sys_price_aggregation
        <where>
            <if test="sku != null and sku != ''">
                AND sku = #{sku}
            </if>
            <if test="spec != null and spec != ''">
                AND spec = #{spec}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>


    <!-- 插入或更新价格聚合数据 -->
    <insert id="insertOrUpdatePriceAggregation">
        INSERT INTO sys_price_aggregation (sku, spec, min_price, max_price, version)
        VALUES (#{sku}, #{spec}, #{minPrice}, #{maxPrice}, 1)
        ON DUPLICATE KEY UPDATE min_price   = #{minPrice},
                                max_price   = #{maxPrice},
                                version     = version + 1,
                                update_time = CURRENT_TIMESTAMP
    </insert>

    <!-- 乐观锁更新价格聚合数据 -->
    <update id="updatePriceAggregationWithVersion">
        UPDATE sys_price_aggregation
        SET min_price   = #{minPrice},
            max_price   = #{maxPrice},
            version     = version + 1,
            update_time = CURRENT_TIMESTAMP
        WHERE sku = #{sku}
          AND spec = #{spec}
          AND version = #{version}
    </update>

    <!-- 删除价格聚合数据 -->
    <delete id="deletePriceAggregation">
        DELETE FROM sys_price_aggregation
        WHERE sku = #{sku}
          AND spec = #{spec}
    </delete>

    <!-- 批量删除过期的价格聚合数据（没有对应在售商品的数据） -->
    <delete id="deleteExpiredPriceAggregation">
        DELETE spa FROM sys_price_aggregation spa
        WHERE NOT EXISTS (
            SELECT 1 FROM knet_product kp
            WHERE kp.sku = spa.sku
              AND kp.spec = spa.spec
              AND kp.status = 'ON_SALE'
              AND kp.price > 0
              AND kp.del_flag = 0
        )
    </delete>

</mapper>
