package com.knet.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.service.ISkuCacheService;
import com.knet.goods.service.ISysSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.knet.common.constants.GoodsServicesConstants.*;

/**
 * SKU缓存服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
public class SkuCacheServiceImpl implements ISkuCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ISysSkuService sysSkuService;

    @Override
    public void initSkuCache() {
        try {
            RedisCacheUtil.hmdel(SKU_CACHE_KEY);
            // 从数据库加载所有SKU数据
            List<String> skus = sysSkuService.selectAllDistinctSkus();
            if (CollUtil.isNotEmpty(skus)) {
                // 批量写入Redis Hash
                Map<String, Object> skuMap = skus.stream()
                        .collect(Collectors.toMap(skuIndexed -> skuIndexed, skuIndexed -> "1"));
                RedisCacheUtil.hmset(SKU_CACHE_KEY, skuMap, SKU_CACHE_KEY_EXPIRED_TIME);
                log.info("成功加载{}条SKU数据到缓存", skuMap.size());
            }
        } catch (Exception e) {
            log.error("初始化SKU缓存失败", e);
            throw new RuntimeException("初始化SKU缓存失败", e);
        }
    }

    @Override
    public void refreshSkuCache() {
        log.info("开始刷新SKU缓存...");
        initSkuCache();
        log.info("SKU缓存刷新完成");
    }

    @Override
    public Set<String> matchSkus(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new HashSet<>();
        }
        //清洗数据 去掉空格，去掉-符号
        final String formattedKeyword = keyword.replaceAll(" ", "").replaceAll("-", "");
        try {
            // 获取所有缓存的SKU数据
            Map<Object, Object> allSkuData = RedisCacheUtil.hmget(SKU_CACHE_KEY);
            if (allSkuData.isEmpty()) {
                log.warn("SKU缓存为空，尝试重新初始化缓存");
                initSkuCache();
                allSkuData = RedisCacheUtil.hmget(SKU_CACHE_KEY);
            }
            // 转换关键词为小写进行模糊匹配
            Set<String> matchedSkus = allSkuData.keySet().parallelStream()
                    .map(Object::toString)
                    .filter(StrUtil::isNotBlank)
                    .filter(sku -> sku.toLowerCase().contains(formattedKeyword.toLowerCase()))
                    .sorted(String.CASE_INSENSITIVE_ORDER)
                    .collect(Collectors.toCollection(LinkedHashSet::new));
            log.debug("关键词'{}'匹配到{}个SKU", keyword, matchedSkus.size());
            return matchedSkus;
        } catch (Exception e) {
            log.error("SKU模糊匹配失败，关键词: {}", keyword, e);
            return new HashSet<>();
        }
    }

    @Override
    public void addSkuToCache(String sku, String skuIndexed) {
        if (StrUtil.isBlank(sku) || StrUtil.isBlank(skuIndexed)) {
            log.warn("SKU或SKU检索索引为空，无法添加到缓存: sku={}, skuIndexed={}", sku, skuIndexed);
            return;
        }
        try {
            redisTemplate.opsForHash().put(SKU_CACHE_KEY, sku, skuIndexed);
            log.debug("成功添加SKU到缓存: sku={}, skuIndexed={}", sku, skuIndexed);
        } catch (Exception e) {
            log.error("添加SKU到缓存失败: sku={}, skuIndexed={}", sku, skuIndexed, e);
        }
    }

    @Override
    public void removeSkuFromCache(String sku) {
        if (StrUtil.isBlank(sku)) {
            log.warn("SKU为空，无法从缓存中删除");
            return;
        }
        try {
            redisTemplate.opsForHash().delete(SKU_CACHE_KEY, sku);
            log.debug("成功从缓存中删除SKU: {}", sku);
        } catch (Exception e) {
            log.error("从缓存中删除SKU失败: {}", sku, e);
        }
    }

    @Override
    public void initKnetProductRemarkCache() {
        try {
            RedisCacheUtil.hmdel(KNET_PRODUCT_REMARK_CACHE_KEY);
            // 从数据库加载所有条knet_product和remark数据
            List<String> remarks = sysSkuService.selectAllDistinctRemarks();
            if (CollUtil.isNotEmpty(remarks)) {
                // 批量写入Redis Hash
                Map<String, Object> remarksMap = remarks.stream()
                        .collect(Collectors.toMap(name -> name, name -> "1"));
                RedisCacheUtil.hmset(KNET_PRODUCT_REMARK_CACHE_KEY, remarksMap, KNET_PRODUCT_REMARK_CACHE_KEY_EXPIRED_TIME);
                log.info("成功加载{}条knet_product remark数据到缓存", remarksMap.size());
            }
        } catch (Exception e) {
            log.error("初始化条knet_product remark缓存失败", e);
            throw new RuntimeException("初始化条knet_product remark缓存失败", e);
        }
    }

    @Override
    public void refreshProductRemarkCache() {
        log.info("开始刷新knet_product remark缓存...");
        initKnetProductRemarkCache();
        log.info("knet_product remark缓存刷新完成");
    }

    @Override
    public Set<String> matchProductsByRemark(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new HashSet<>();
        }
        //清洗数据 去掉空格
        final String formattedKeyword = keyword.replaceAll(" ", "");
        try {
            // 获取所有缓存的SKU remark数据
            Map<Object, Object> allRemarkData = RedisCacheUtil.hmget(KNET_PRODUCT_REMARK_CACHE_KEY);
            if (allRemarkData.isEmpty()) {
                log.warn("Knet Product remark缓存为空，尝试重新初始化缓存");
                initKnetProductRemarkCache();
                allRemarkData = RedisCacheUtil.hmget(KNET_PRODUCT_REMARK_CACHE_KEY);
            }
            // 根据remark内容进行模糊匹配，返回匹配的SKU
            // 转换关键词为小写进行模糊匹配
            Set<String> matchedRemarks = allRemarkData.keySet().parallelStream()
                    .map(Object::toString)
                    .filter(StrUtil::isNotBlank)
                    .filter(name -> name.toLowerCase().contains(formattedKeyword.toLowerCase()))
                    .sorted(String.CASE_INSENSITIVE_ORDER)
                    .collect(Collectors.toCollection(LinkedHashSet::new));
            log.debug("关键词'{}'在remark中匹配到{}个", keyword, matchedRemarks.size());
            return matchedRemarks;
        } catch (Exception e) {
            log.error("knet product remark模糊匹配失败，关键词: {}", keyword, e);
            return new HashSet<>();
        }
    }
}
