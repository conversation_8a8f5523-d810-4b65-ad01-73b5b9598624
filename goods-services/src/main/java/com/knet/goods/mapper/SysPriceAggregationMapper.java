package com.knet.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.goods.model.dto.resp.PriceAggregationResp;
import com.knet.goods.model.entity.SysPriceAggregation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:25
 * @description: 价格聚合Mapper
 */
@Mapper
public interface SysPriceAggregationMapper extends BaseMapper<SysPriceAggregation> {

    /**
     * 创建价格聚合表
     */
    void createPriceAggregationTable();

    /**
     * 查询价格聚合数据
     *
     * @param sku  商品SKU
     * @param spec 商品规格（可选）
     * @return 价格聚合数据列表
     */
    List<PriceAggregationResp> selectPriceAggregation(@Param("sku") String sku, @Param("spec") String spec);


    /**
     * 插入或更新价格聚合数据
     *
     * @param sku      商品SKU
     * @param spec     商品规格
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 影响行数
     */
    int insertOrUpdatePriceAggregation(@Param("sku") String sku, @Param("spec") String spec,
                                       @Param("minPrice") Long minPrice, @Param("maxPrice") Long maxPrice);

    /**
     * 乐观锁更新价格聚合数据
     *
     * @param sku      商品SKU
     * @param spec     商品规格
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param version  版本号
     * @return 影响行数
     */
    int updatePriceAggregationWithVersion(@Param("sku") String sku, @Param("spec") String spec,
                                          @Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice,
                                          @Param("version") Integer version);

    /**
     * 删除价格聚合数据
     *
     * @param sku  商品SKU
     * @param spec 商品规格
     * @return 影响行数
     */
    int deletePriceAggregation(@Param("sku") String sku, @Param("spec") String spec);

    /**
     * 批量删除过期的价格聚合数据（没有对应在售商品的数据）
     *
     * @return 删除的记录数
     */
    int deleteExpiredPriceAggregation();
}
