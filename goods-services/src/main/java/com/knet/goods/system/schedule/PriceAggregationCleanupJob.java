package com.knet.goods.system.schedule;

import com.knet.goods.service.IPriceAggregationService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/5 16:00
 * @description: 价格聚合数据清理定时任务
 */
@Slf4j
@Component
public class PriceAggregationCleanupJob {

    @Resource
    private IPriceAggregationService priceAggregationService;

    /**
     * todo 清理过期的价格聚合数据
     * 删除没有对应在售商品的价格聚合记录
     * <p>
     * 建议执行频率：每天凌晨2点执行一次
     * Cron表达式：0 0 2 * * ?
     */
    @XxlJob("priceAggregationCleanupJob")
    public void cleanupExpiredPriceAggregation() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            String message = "开始执行价格聚合数据清理任务";
            log.info(message);
            XxlJobHelper.log(message);
            // 执行清理操作
            int deletedCount = priceAggregationService.cleanupExpiredPriceAggregation();
            stopWatch.stop();
            String successMessage = String.format(
                    "价格聚合数据清理任务执行完成，耗时: %d ms，清理记录数: %d",
                    stopWatch.getTotalTimeMillis(),
                    deletedCount
            );
            log.info(successMessage);
            XxlJobHelper.log(successMessage);
        } catch (Exception e) {
            stopWatch.stop();
            String errorMessage = String.format(
                    "价格聚合数据清理任务执行失败，耗时: %d ms，错误信息: %s",
                    stopWatch.getTotalTimeMillis(),
                    e.getMessage()
            );
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            throw e;
        }
    }

    /**
     * todo 手动触发价格聚合数据清理
     * 可用于紧急情况下的手动清理
     */
    @XxlJob("manualPriceAggregationCleanup")
    public void manualCleanupExpiredPriceAggregation() {
        try {
            String jobParam = XxlJobHelper.getJobParam();
            String message = String.format("手动触发价格聚合数据清理任务，参数: %s", jobParam);
            log.info(message);
            XxlJobHelper.log(message);
            // 执行清理操作
            cleanupExpiredPriceAggregation();
        } catch (Exception e) {
            String errorMessage = String.format("手动价格聚合数据清理任务执行失败: %s", e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            throw e;
        }
    }
}
