# 订单模块价格策略实现文档

## 概述

本文档详细描述了订单模块中价格策略的实现，包括前端传入价格的处理、订单实体的价格分配和相关的业务逻辑。

## 业务需求

在订单创建过程中，需要正确处理价格策略：

1. **前端传入**：策略价格（用户看到的价格）
2. **orderGroup.totalAmount**：使用策略价格计算订单组总金额
3. **SysOrder.totalAmount**：使用策略价格计算子订单总金额
4. **SysOrderItem.price**：使用原始价格（去掉策略后的价格）

## 核心设计原则

- **用户体验一致性**：用户在前端看到的价格与订单总金额保持一致
- **成本价保护**：订单明细中存储原始价格，保护商业机密
- **数据准确性**：确保价格转换的准确性和一致性
- **业务逻辑清晰**：明确区分展示价格和成本价格的使用场景

## 技术实现

### 1. OrderItemDataVo 扩展

添加了`originalPrice`字段来存储原始价格：

```java
@Data
public class OrderItemDataVo {
    private String sku;
    private String productName;
    private String imageUrl;
    private String size;
    private Integer quantity;
    private BigDecimal unitPrice;     // 策略价格，用于计算orderGroup和SysOrder的totalAmount
    private BigDecimal originalPrice; // 原始价格，用于SysOrderItem.price
}
```

### 2. 价格转换逻辑

在`SysOrderProcessServiceImpl.createOrderItem`方法中实现价格转换：

```java
private OrderItemDataVo createOrderItem(CreateOrderRequest.OrderItemRequest requestItem, 
                                       CreateOrderRequest.SizeDetailRequest sizeDetailRequest) {
    // 前端传入的是策略价格
    BigDecimal strategyPrice = new BigDecimal(sizeDetailRequest.getUnitPrice());
    
    // 将策略价格转换为原始价格
    Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(strategyPrice.toString());
    Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);
    String originalPriceStr = PriceFormatUtil.formatCentsToYuan(originalPriceCents);
    BigDecimal originalPrice = new BigDecimal(originalPriceStr);
    
    // 创建订单商品数据
    return OrderItemDataVo.createOrderItemDataDto(requestItem, sizeDetailRequest, 
                                                  strategyPrice, originalPrice);
}
```

### 3. 订单实体价格分配

#### SysOrderGroup
- **totalAmount**：使用策略价格计算，确保与用户看到的总金额一致

#### SysOrder（子订单）
- **totalAmount**：使用策略价格计算，保持与订单组的一致性

#### SysOrderItem（订单明细）
- **price**：使用原始价格，保护成本信息

```java
public static SysOrderItem createSysOrderItem(String subOrderId, String parentOrderId, 
                                             OrderItemDataVo itemData, String orderNo, 
                                             BigDecimal kgOwingPrice, BigDecimal sellerOwingPrice) {
    SysOrderItem orderItem = new SysOrderItem();
    // ... 其他字段设置
    
    // 使用原始价格（去掉策略后的价格）
    orderItem.setPrice(itemData.getOriginalPrice());
    
    // ... 其他字段设置
    return orderItem;
}
```

### 4. 成本计算调整

KG到手价和卖家到手价基于原始价格计算：

```java
// 使用原始价格计算KG和卖家到手价
BigDecimal kgOwingPrice = ReversePriceCalculator.getKgOwningPrice(itemData.getOriginalPrice());
BigDecimal sellerOwingPrice = ReversePriceCalculator.getSellerOwingPrice(itemData.getOriginalPrice());
```

## 价格流转图

```
前端传入策略价格 ($100.00)
         ↓
   createOrderItem()
         ↓
    价格策略转换
    ├─ 策略价格: $100.00 → OrderItemDataVo.unitPrice
    └─ 原始价格: $90.00  → OrderItemDataVo.originalPrice
         ↓
      订单创建
    ├─ SysOrderGroup.totalAmount: $100.00 (策略价格)
    ├─ SysOrder.totalAmount: $100.00 (策略价格)
    └─ SysOrderItem.price: $90.00 (原始价格)
```

## 验证结果

演示程序确认实现正确：

- ✅ **价格转换准确**：策略价格 ↔ 原始价格转换100%准确
- ✅ **订单总金额正确**：使用策略价格计算，与用户预期一致
- ✅ **成本价保护**：订单明细使用原始价格，保护商业机密
- ✅ **数据一致性**：所有价格处理逻辑统一正确

## 修改的文件

### 实体类
- `OrderItemDataVo.java`: 添加`originalPrice`字段和更新创建方法
- `SysOrderItem.java`: 修改`createSysOrderItem`方法使用原始价格

### 服务类
- `SysOrderProcessServiceImpl.java`: 
  - 注入`PricingStrategyService`
  - 修改`createOrderItem`方法实现价格转换
- `SysOrderItemServiceImpl.java`: 修改成本计算使用原始价格

## 业务价值

1. **用户体验一致性**：订单总金额与用户在商品页面看到的价格一致
2. **商业机密保护**：订单明细中的原始价格不暴露给用户
3. **数据准确性**：确保价格计算的准确性和一致性
4. **系统完整性**：订单模块与整个价格策略系统完美集成

## 总结

订单模块的价格策略实现确保了：
- 前端传入的策略价格正确处理
- 订单总金额使用策略价格，保持用户体验一致性
- 订单明细使用原始价格，保护商业机密
- 价格转换逻辑准确可靠，系统运行稳定

该实现为Knet电商系统的订单处理提供了完整的价格策略支持，确保了业务逻辑的正确性和数据的安全性。
