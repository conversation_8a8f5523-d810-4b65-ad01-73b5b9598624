# 价格查询系统

## 概述

基于手动缓存管理、RabbitMQ消息驱动架构和增强表结构的高可用价格查询解决方案，支持QPS 1000的查询性能。

## 系统架构

### 1. 数据层
- **sys_price_aggregation**: 价格聚合持久化存储表，支持乐观锁
- **sys_message_retry**: 消息重试表，处理MQ发送失败的消息

### 2. 缓存层
- **Redis Hash结构**: `price:{sku}:{spec}` 格式缓存最新聚合结果
- **双删除策略**: 确保缓存与数据库数据一致性

### 3. 消息层
- **价格变动事件**: 商品操作触发价格变动消息
- **异步处理**: 消息消费者异步处理价格聚合
- **重试机制**: 失败消息自动重试，最大3次

### 4. 服务层
- **并行查询**: CompletableFuture实现批量查询并行处理
- **缓存优先**: 优先从Redis查询，缓存未命中时查询数据库

## 核心功能

### 1. 价格查询接口

#### 单个SKU查询
```http
GET /api/price/query?sku=TEST-SKU-001&spec=42
```

#### 批量查询
```http
POST /api/price/batch-query
Content-Type: application/json

{
  "products": [
    {
      "sku": "TEST-SKU-001",
      "spec": "42"
    },
    {
      "sku": "TEST-SKU-002", 
      "spec": "43"
    }
  ]
}
```

### 2. 价格变动事件触发

系统在以下操作时自动发送价格变动事件：
- **createProducts**: 创建商品时发送CREATE事件
- **offSale**: 商品下架时发送OFF_SALE事件
- **updatePrice**: 价格更新时发送UPDATE_PRICE事件

### 3. 价格聚合数据清理

系统会自动清理过期的价格聚合数据：
- **自动清理**: 当某个sku+spec组合的所有商品都下架时，自动删除对应的价格聚合数据
- **定时清理**: 每天凌晨2点执行清理任务，删除没有对应在售商品的价格聚合记录
- **手动清理**: 提供手动清理接口，支持紧急情况下的数据清理

### 4. 消息重试机制

- **自动重试**: 消息发送失败时自动保存到重试表
- **定时任务**: 每5分钟执行一次重试任务
- **指数退避**: 重试间隔递增，避免系统压力
- **最大重试**: 最多重试3次，超过后标记为失败

## 配置说明

### RabbitMQ配置
```yaml
# 价格变动交换机和队列
price-change-exchange: 
  type: topic
  durable: true

price-change-queue.goods-services:
  durable: true
  routing-key: price.change
```

### Redis配置
```yaml
# 价格缓存配置
price:
  cache:
    prefix: "price:"
    ttl: 3600  # 1小时过期
```

### 定时任务配置
```yaml
# XXL-Job配置
xxl:
  job:
    executor:
      appname: goods-services
    admin:
      addresses: http://localhost:8080/xxl-job-admin

# 价格聚合数据清理任务
# 任务名称: priceAggregationCleanupJob
# Cron表达式: 0 0 2 * * ? (每天凌晨2点执行)
# 手动清理任务: manualPriceAggregationCleanup
```

## 性能优化

### 1. 缓存策略
- **缓存预热**: 系统启动时预加载热点数据
- **缓存穿透**: 空结果也进行缓存，避免频繁查询数据库
- **缓存雪崩**: 设置随机过期时间，避免同时失效

### 2. 并发处理
- **线程池**: 使用专用线程池处理批量查询
- **CompletableFuture**: 并行处理多个查询请求
- **乐观锁**: 使用版本号避免并发更新冲突

### 3. 数据库优化
- **索引优化**: 在sku、spec、status字段建立复合索引
- **分页查询**: 大批量查询使用分页避免内存溢出
- **连接池**: 合理配置数据库连接池参数

## 监控告警

### 1. 性能监控
- **QPS监控**: 监控查询接口QPS是否达到1000目标
- **响应时间**: 监控平均响应时间和P99响应时间
- **缓存命中率**: 监控Redis缓存命中率

### 2. 异常告警
- **消息积压**: 监控RabbitMQ队列消息积压情况
- **重试失败**: 监控消息重试失败次数
- **缓存异常**: 监控Redis连接异常和操作失败

## 使用示例

### 1. 单个价格查询
```java
@Resource
private IPriceAggregationService priceAggregationService;

public void queryPrice() {
    PriceAggregationResp result = priceAggregationService.queryPrice("TEST-SKU-001", "42");
    System.out.println("价格: " + result.getPrice());
    System.out.println("状态: " + result.getStatus());
}
```

### 2. 批量价格查询
```java
List<PriceQueryRequest.ProductQuery> products = Arrays.asList(
    PriceQueryRequest.ProductQuery.builder()
        .sku("TEST-SKU-001")
        .spec("42")
        .build()
);

List<PriceAggregationResp> results = priceAggregationService.batchQueryPrice(products);
```

### 3. 价格聚合数据管理
```java
@Resource
private IPriceAggregationService priceAggregationService;

// 删除特定sku+spec的价格聚合数据
public void deletePriceData() {
    boolean deleted = priceAggregationService.deletePriceAggregation("TEST-SKU-001", "42");
    System.out.println("删除结果: " + deleted);
}

// 清理所有过期的价格聚合数据
public void cleanupExpiredData() {
    int cleanedCount = priceAggregationService.cleanupExpiredPriceAggregation();
    System.out.println("清理记录数: " + cleanedCount);
}
```

## 故障处理

### 1. 缓存故障
- **Redis宕机**: 自动降级到数据库查询
- **缓存数据不一致**: 手动清理缓存，重新加载

### 2. 消息故障  
- **RabbitMQ宕机**: 消息保存到重试表，恢复后自动重试
- **消费者异常**: 消息拒绝后进入死信队列，人工处理

### 3. 数据库故障
- **主库故障**: 切换到从库查询（只读）
- **连接池耗尽**: 增加连接池大小或优化查询

## 扩展计划

1. **多级缓存**: 增加本地缓存减少Redis访问
2. **读写分离**: 查询走从库，写入走主库
3. **分库分表**: 按SKU哈希分表提升性能
4. **异地多活**: 支持多机房部署和容灾切换
