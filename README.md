# 项目基础架构

| 层级      | 技术/框架                           | 说明                   |
|---------|:--------------------------------|----------------------|
| 微服务框架   | Spring Cloud 2021.0.5           | 微服务基础框架              |
| 微服务组件   | Spring Cloud Alibaba 2021.0.5.0 | 阿里巴巴微服务组件            |
| 应用框架    | Spring Boot 2.7.18              | 应用开发框架               |
| JDK     | JDK 17 （OpenJDK）                | Java开发环境             |
| 服务注册与配置 | Nacos 2.2.0                     | 服务注册、发现与配置中心         |
| 服务网关    | Spring Cloud Gateway            | API网关                |
| 服务调用    | OpenFeign                       | 声明式REST客户端           |
| 负载均衡    | Spring Cloud LoadBalancer       | 客户端负载均衡              |
| 数据访问    | MyBatis-Plus 3.5.12             | ORM框架                |
| 数据库连接池  | HikariCP                        | 数据库连接池               |
| 数据库组件   | mybatis.spring  2.3.0           | 数据库组件                |
| 数据库     | MySQL 8                         | 关系型数据库               |
| 缓存      | Redis   6.0                     | 分布式缓存                |
| 消息队列    | RabbitMQ 3.12.6                 | 消息中间件                |
| 任务调度    | XXL-Job 3.1.1                   | 分布式任务调度平台            |
| 分布式锁    | Redis + AOP                     | 基于Redis的分布式锁实现       |
| 限流控制    | Redis + AOP                     | 基于Redis的令牌桶算法实现      |
| 权限控制    | JWT + AOP                       | 基于JWT的权限控制           |
| 日志记录    | AOP                             | 基于AOP的日志记录           |
| 容器化     | Docker                          | 应用容器化                |
| 流量控制    | Spring Retry                    | 基于springRetry 应用内部重试 |

## 技术栈

- **微服务框架**: 阿里巴巴微服务
    - **服务注册与配置中心**: Nacos 2.2.0
        - **地址**: [Nacos测试环境](http://192.168.6.5:8844/nacos/)
        - **账号**: `nacos`
        - **密码**: `nacos`
    - **服务间调用**: `OpenFeign`
    - **API网关**: `Spring Gateway`
        - 实现服务间的权限控制SSO（已实现），统一日志收集（暂时不需要）
    - **分布式锁**:  `@DistributedLock(key = "'deleteUser:'+#id", expire = 2)` 基于Redis setnx 实现
    - **流量控制**: `@RateLimiter` redis 实现令牌桶算法
    - **负载均衡**: 基于`loadbalancer`的实现,默认采用轮训策略，同一个服务的不同实例之间采用轮训策略
    - **事务控制**: 冗余设计，尽量将跨服务的事务控制在一个服务中，如果不可避免，采用MQ消息队列实现最终一致性（prd）
    - **权限控制**: `@PermissionCheck` 基于AOP实现
    - **日志记录**: `@Loggable` 基于AOP实现
    - **docker 支持**: 各个模块有dockerfile文件，可以直接构建镜像,`docker build -t xxx-services .`
    - **MQ消息队列**:  `rabbitMQ 3.12.6`（安装位置/app/rabbitmq-docker）`http://192.168.6.5:15672/#/ admin/admin`
    - **分布式事务控制**: 利用MQ消息队列实现最终一致性 SAGA+补偿机制
    - **定时任务框架**: `XXL-Job 3.1.1` `http://192.168.6.5:7777/xxl-job-admin/ admin/123456`
    - **接口文档** `SpringDoc API 3.0 + Apifox`
    - **请求重试** `Spring Retry` 应用内部重试，openFeign（应用实例层级 不重试、不熔断）
    - **延迟队列** `Redis Zset + xxl-job 定时任务` 支持自定义的延迟信息实现（背景aws上的rabbit_mq 不支持延迟信息插件，被迫实现）

## 预计实现的特性

- **多数据源**: `DataSource`实现主从读写分离
- **流量控制**: `Sentinel` 成熟可靠的方案，需要部署独立服务
- **链路追踪**: `SkyWalking` 零侵入，减少手动埋点的过程，负面效果比较耗费资源
- **分布式号段发布服务** 提取 RandomStrUtil中的方法实现
- **微服务管理** `k8s`
- **rocketMQ** `🚀当业务发展到一定数量时，启用🚀MQ`
-

## 模块概述

```lua
knet
├── gateway           -- 网关模块，负责路由转发和权限校验[7000]
├── common            -- 项目公共模块，定义项目中的基础类，基本的公用策略（服务实现）一些其他项目需要用的请求返回dto,建议也放在common中
├── goods-services    -- 商品模块，包括商品数据、库存、价格管理[7001]
├── user-services     -- 用户模块，包括用户信息管理、认证和权限控制、购物车  (prd 收藏夹)[7002]
├── oauth-services    -- 授权服务，用于与第三方通信鉴权[7003]
├── order-services    -- 订单服务，订单相关 物流服务暂且聚合在一起 [7004]
├── payment-services  -- 支付服务，支付相关包含：用户钱包、流水、支付记录、第三方支付平台等 [7005]
├── notification-services  -- 全平台通知中枢，统一处理所有业务模块的通知发送、管理和推送逻辑 [7006]
└── delayed-services  -- 全平台延迟消息中枢，支持自定义的延迟信息实现 [7007]
```

<a href="https://apifox.com/apidoc/shared-b65313ef-211f-47c2-8f75-cb929d7a4b30">接口文档</a></p>

## 注意

- **配置**: 本地启动需要更改 6.5 nacos地址
- **外部请求**: 项目中的所有外部请求统一走gateway，gateway会对请求进行权限校验。
- **内部请求**: 公司内部系统调用，直接走服务器Ip+服务port即可，不需要走gateway。
- **约定**: 项目禁用循环依赖
- **文档**: ../doc 存在部分关键设计的md文档